import type { Config } from 'tailwindcss'

const config: Config = {
  darkMode: 'class',
  content: [
    './src/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          light: '#C69749',
          DEFAULT: '#C69749',
          dark: '#856930',
        },
        background: {
          light: '#F9F5ED',
        }
      },
    },
  },
  plugins: [],
}

export default config 