'use server'

import { Franchise } from '@/types/franchise';

export async function fetchFranchises(page: number = 1, limit: number = 8): Promise<Franchise[]> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Return mock data
  return Array.from({ length: limit }, (_, i) => ({
    id: `${page}-${i + 1}`,
    name: 'HUBC<PERSON>',
    username: 'hubcv',
    coverPhoto: '/franchise/hubcv-1-3.png',
    gallery: ['/franchise/hubcv-1-1.png', '/franchise/hubcv-1-2.png', '/franchise/hubcv-1-3.png'],
    logo: '/logo/logo-2.svg',
    industry: 'Service',
    category: 'Human Resource',
    address: 'Mumbai, Maharashtra',
    description: 'Leading HR tech platform revolutionizing recruitment',
    totalInvestment: 5000000,
    carpetArea: 1200,
    costPerShare: 50000,
    status: 'Fundraising' as const,
    totalShares: 100,
    soldShares: 60,
    minimumShares: 2,
    products: [{
      id: '1',
      name: 'Resume Builder',
      description: 'AI-powered resume builder',
      price: 999,
      image: '/products/resume.png',
      category: 'Software'
    }],
    franchisees: [{
      id: '1',
      name: '<PERSON> Doe',
      avatar: '/avatars/user.png',
      location: 'Delhi',
      joinedDate: '2024-01-01',
      shareCount: 5
    }],
    financialProjections: [{
      year: 2024,
      revenue: 12000000,
      expenses: 8000000,
      profit: 4000000,
      roi: 0.8
    }],
    locationAnalysis: {
      footfall: 5000,
      peakHours: ['10:00', '14:00', '18:00'],
      demographicData: {
        ageGroups: { '18-24': 30, '25-34': 45 },
        income: { 'Low': 20, 'Medium': 50, 'High': 30 }
      },
      competitorCount: 3,
      marketPotential: 0.85
    },
    reviews: [{
      id: '1',
      userId: '1',
      userName: 'Alice',
      userAvatar: '/avatars/alice.png',
      rating: 4.5,
      comment: 'Great opportunity',
      createdAt: '2024-01-01'
    }],
    rating: 4.2
  }));
} 