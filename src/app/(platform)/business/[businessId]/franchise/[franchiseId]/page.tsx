'use client'

import React, { useState } from 'react';
import Image from 'next/image';
import Header from '@/components/Header';
import PaymentModal from '@/components/PaymentModal';
import { Franchise } from '@/types/franchise';
import { MapPin, Star } from 'lucide-react';

// Mock data - Replace with actual data fetching
const mockFranchise: Franchise = {
  id: '1',
  name: 'HUBC<PERSON>',
  username: 'hubcv',
  coverPhoto: '/franchise/hubcv-1-3.png',
  gallery: [
    '/franchise/hubcv-1-1.png',
    '/franchise/hubcv-1-2.png',
    '/franchise/hubcv-1-3.png',
    '/franchise/hubcv-1-4.png',
  ],
  logo: '/logo/logo-2.svg',
  industry: 'Service',
  category: 'Human Resource',
  address: 'Mumbai, Maharashtra',
  description: 'HUBCV is a leading HR tech platform that helps businesses streamline their recruitment process. With our franchise model, you can be part of the growing HR tech industry.',
  totalInvestment: 5000000,
  carpetArea: 1200,
  costPerShare: 50000,
  status: 'Fundraising',
  totalShares: 100,
  soldShares: 60,
  minimumShares: 2,
  products: [
    {
      id: 'p1',
      name: 'Resume Builder Pro',
      description: 'AI-powered resume builder with professional templates',
      price: 999,
      image: '/products/product-1.jpg',
      category: 'Software'
    },
    {
      id: 'p2',
      name: 'Resume Builder Pro',
      description: 'AI-powered resume builder with professional templates',
      price: 999,
      image: '/products/product-2.jpg',
      category: 'Software'
    },
    {
      id: 'p3',
      name: 'Resume Builder Pro',
      description: 'AI-powered resume builder with professional templates',
      price: 999,
      image: '/products/product-3.jpg',
      category: 'Software'
    },
    {
      id: 'p4',
      name: 'Resume Builder Pro',
      description: 'AI-powered resume builder with professional templates',
      price: 999,
      image: '/products/product-4.jpg',
      category: 'Software'
    },
    {
      id: 'p5',
      name: 'Resume Builder Pro',
      description: 'AI-powered resume builder with professional templates',
      price: 999,
      image: '/products/product-5.jpg',
      category: 'Software'
    },
    {
      id: 'p6',
      name: 'Resume Builder Pro',
      description: 'AI-powered resume builder with professional templates',
      price: 999,
      image: '/products/product-6.jpg',
      category: 'Software'
    },
    {
      id: 'p7',
      name: 'Resume Builder Pro',
      description: 'AI-powered resume builder with professional templates',
      price: 999,
      image: '/products/product-7.jpg',
      category: 'Software'
    },
    {
      id: 'p8',
      name: 'Resume Builder Pro',
      description: 'AI-powered resume builder with professional templates',
      price: 999,
      image: '/products/product-8.jpg',
      category: 'Software'
    },
    // Add more products...
  ],
  franchisees: [
      {
        id: '1',
        name: 'John Doe',
        avatar: '/avatar/avatar-m-1.png',
        joinedDate: '2023-01-15',
        shareCount: 5
      },
      {
        id: '2',
        name: 'Jane Smith',
        avatar: '/avatar/avatar-f-2.png',
        joinedDate: '2023-02-20',
        shareCount: 3
      },
      {
        id: '3',
        name: 'Michael Brown',
        avatar: '/avatar/avatar-m-3.png',
        joinedDate: '2023-03-25',
        shareCount: 2
      },
      {
        id: '4',
        name: 'Emily Davis',
        avatar: '/avatar/avatar-m-4.png',
        joinedDate: '2023-04-30',
        shareCount: 1
      },
      {
        id: '5',
        name: 'Daniel Wilson',
        avatar: '/avatar/avatar-f-5.png',
        joinedDate: '2023-05-05',
        shareCount: 4
      },
      {
        id: '6',
        name: 'Olivia Taylor',
        avatar: '/avatar/avatar-f-6.png',
        joinedDate: '2023-06-10',
        shareCount: 2
      },
      {
        id: '7',
        name: 'William Moore',
        avatar: '/avatar/avatar-f-1.png',
        joinedDate: '2023-07-15',
        shareCount: 3
      },
      {
        id: '8',
        name: 'Olivia Taylor',
        avatar: '/avatar/avatar-m-6.png',
        joinedDate: '2023-06-10',
        shareCount: 2
      },
    // Add more franchisees...
  ],
  financialProjections: [
    {
      year: 2024,
      revenue: 12000000,
      expenses: 8000000,
      profit: 4000000,
      roi: 0.8
    },
    // Add more projections...
  ],
  locationAnalysis: {
    footfall: 5000,
    peakHours: ['10:00', '14:00', '18:00'],
    demographicData: {
      ageGroups: {
        '18-24': 30,
        '25-34': 45,
        '35-44': 15,
        '45+': 10
      },
      income: {
        'Low': 20,
        'Medium': 50,
        'High': 30
      }
    },
    competitorCount: 3,
    marketPotential: 0.85
  },
  reviews: [
    {
      id: 'r1',
      userId: 'u1',
      userName: 'Alice Smith',
      userAvatar: '/avatar/avatar-f-1.png',
      rating: 4.5,
      comment: 'Great franchise opportunity with excellent support',
      createdAt: '2024-02-15'
    },
    {
      id: 'r2',
      userId: 'u2',
      userName: 'John Doe',
      userAvatar: '/avatar/avatar-m-1.png',
      rating: 4.5,
      comment: 'Great franchise opportunity with excellent support',
      createdAt: '2024-02-15'
    },
    
    // Add more reviews...
  ],
  rating: 4.2
};

function FranchiseDetail() {
  const [visibleFranchisees, setVisibleFranchisees] = useState(4);
  const [visibleProducts, setVisibleProducts] = useState(4);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);

  const loadMoreFranchisees = () => {
    setVisibleFranchisees(prev => prev + 4);
  };

  const loadMoreProducts = () => {
    setVisibleProducts(prev => prev + 4);
  };

  return (
    <main className="min-h-screen bg-stone-100">
      <Header />
      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={() => setIsPaymentModalOpen(false)}
        franchiseData={{
          name: mockFranchise.name,
          logo: mockFranchise.logo,
          address: mockFranchise.address,
          totalShares: mockFranchise.totalShares,
          soldShares: mockFranchise.soldShares,
          costPerShare: mockFranchise.costPerShare,
        }}
      />
      <div className="pt-[65px]">
        {/* Image Gallery */}
        <div className="max-w-7xl mx-auto px-8 mt-6 sm:px-6 lg:px-8">
          <div className="relative h-[40vh] grid grid-cols-4 grid-rows-2 gap-2">
            <div className="col-span-2 row-span-2 relative rounded-l-xl overflow-hidden">
              <Image
                src={mockFranchise.gallery[0]}
                alt={mockFranchise.name}
                fill
                className="object-cover"
              />
            </div>
            <div className="relative  overflow-hidden">
              <Image
                src={mockFranchise.gallery[1]}
                alt={mockFranchise.name}
                fill
                className="object-cover"
              />
            </div>
            <div className="relative rounded-tr-xl overflow-hidden">
              <Image
                src={mockFranchise.gallery[2]}
                alt={mockFranchise.name}
                fill
                className="object-cover"
              />
            </div>
            <div className="relative overflow-hidden">
              <Image
                src={mockFranchise.gallery[3]}
                alt={mockFranchise.name}
                fill
                className="object-cover"
              />
            </div>
            <div className="relative rounded-br-xl overflow-hidden">
              <Image
                src={mockFranchise.coverPhoto}
                alt={mockFranchise.name}
                fill
                className="object-cover"
              />
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-8">
              {/* Fundraising Card */}
              <section className="bg-white rounded-xl p-6 shadow-sm">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-2xl font-semibold">Fundraising</h2>
                </div>
                
                <div className="space-y-6">
                  <div>
                    <p className="text-sm text-gray-600 mb-2">Available Shares</p>
                    <div className="flex items-center justify-between mb-2">
                      <p className="text-2xl font-semibold">{mockFranchise.totalShares - mockFranchise.soldShares}</p>
                      <p className="text-sm text-gray-600">of {mockFranchise.totalShares} total</p>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-primary rounded-full h-2"
                        style={{
                          width: `${((mockFranchise.totalShares - mockFranchise.soldShares) / mockFranchise.totalShares) * 100}%`
                        }}
                      />
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="flex-1">
                      <p className="text-sm text-gray-600 mb-1">Cost per Share</p>
                      <p className="text-2xl font-semibold">₹{mockFranchise.costPerShare.toLocaleString()}</p>
                    </div>
                    <button 
                      onClick={() => setIsPaymentModalOpen(true)}
                      className="flex-1 bg-primary text-white py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors"
                    >
                      Buy Shares
                    </button>
                  </div>
                </div>
              </section>

              {/* Franchisee List */}
              <section className="bg-white rounded-xl p-6 shadow-sm">
                <h2 className="text-2xl font-semibold mb-4">Current Franchisees</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {mockFranchise.franchisees.slice(0, visibleFranchisees).map(franchisee => (
                    <div key={franchisee.id} className="flex items-center justify-between p-4 border rounded-lg hover:border-primary/30 transition-colors group">
                      <div className="flex items-center space-x-4">
                        <div className="relative h-12 w-12">
                          <Image
                            src={franchisee.avatar}
                            alt={franchisee.name}
                            fill
                            className="object-cover rounded-full"
                          />
                        </div>
                        <div>
                          <h3 className="font-medium">{franchisee.name}</h3>
                          <p className="text-sm text-gray-500">{franchisee.shareCount} shares</p>
                        </div>
                      </div>
                      <button className="text-sm px-3 py-1.5 border border-primary/0 text-primary rounded-lg opacity-0 group-hover:opacity-100 group-hover:border-primary/100 transition-all duration-200 hover:bg-primary hover:text-white">
                        Offer to Buy
                      </button>
                    </div>
                  ))}
                </div>
                {visibleFranchisees < mockFranchise.franchisees.length && (
                  <div className="mt-6 text-center">
                    <button 
                      type="button"
                      onClick={loadMoreFranchisees}
                      className="px-6 py-2 text-primary border border-primary rounded-lg hover:bg-primary hover:text-white transition-colors"
                    >
                      Load More
                    </button>
                  </div>
                )}
              </section>

              {/* Product List */}
              <section className="bg-white rounded-xl p-6 shadow-sm">
                <h2 className="text-2xl font-semibold mb-4">Products & Services</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {mockFranchise.products.slice(0, visibleProducts).map(product => (
                    <div key={product.id} className="border rounded-lg overflow-hidden">
                      <div className="relative h-48">
                        <Image
                          src={product.image}
                          alt={product.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="p-4">
                        <h3 className="font-medium">{product.name}</h3>
                        <p className="text-sm text-gray-600 mt-1">{product.description}</p>
                        <p className="text-primary font-medium mt-2">
                          ₹{product.price.toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
                {visibleProducts < mockFranchise.products.length && (
                  <div className="mt-6 text-center">
                    <button 
                      type="button"
                      onClick={loadMoreProducts}
                      className="px-6 py-2 text-primary border border-primary rounded-lg hover:bg-primary hover:text-white transition-colors"
                    >
                      Load More
                    </button>
                  </div>
                )}
              </section>

              {/* Financial Projections */}
              <section className="bg-white rounded-xl p-6 shadow-sm">
                <h2 className="text-2xl font-semibold mb-4">Financial Projections</h2>
                <div className="space-y-4">
                  {mockFranchise.financialProjections.map(projection => (
                    <div key={projection.year} className="border rounded-lg p-4">
                      <h3 className="font-medium">Year {projection.year}</h3>
                      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mt-2">
                        <div>
                          <p className="text-sm text-gray-600">Revenue</p>
                          <p className="font-medium">₹{projection.revenue.toLocaleString()}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Expenses</p>
                          <p className="font-medium">₹{projection.expenses.toLocaleString()}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Profit</p>
                          <p className="font-medium">₹{projection.profit.toLocaleString()}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">ROI</p>
                          <p className="font-medium">{(projection.roi * 100).toFixed(1)}%</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Location Analysis */}
              <section className="bg-white rounded-xl p-6 shadow-sm">
                <h2 className="text-2xl font-semibold mb-4">Location Analysis</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-medium mb-2">Daily Footfall</h3>
                      <p className="text-2xl font-semibold">{mockFranchise.locationAnalysis.footfall.toLocaleString()}</p>
                    </div>
                    <div>
                      <h3 className="font-medium mb-2">Peak Hours</h3>
                      <div className="flex flex-wrap gap-2">
                        {mockFranchise.locationAnalysis.peakHours.map(hour => (
                          <span key={hour} className="bg-gray-100 px-3 py-1 rounded-full text-sm">
                            {hour}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h3 className="font-medium mb-2">Market Potential</h3>
                      <div className="flex items-center">
                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-primary rounded-full h-2"
                            style={{ width: `${mockFranchise.locationAnalysis.marketPotential * 100}%` }}
                          />
                        </div>
                        <span className="ml-2 text-sm font-medium">
                          {(mockFranchise.locationAnalysis.marketPotential * 100).toFixed(0)}%
                        </span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-medium mb-2">Demographics</h3>
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-sm text-gray-600 mb-2">Age Groups</h4>
                        {Object.entries(mockFranchise.locationAnalysis.demographicData.ageGroups).map(([age, percentage]) => (
                          <div key={age} className="flex items-center mb-2">
                            <span className="w-16 text-sm">{age}</span>
                            <div className="flex-1 bg-gray-200 rounded-full h-2 mx-2">
                              <div
                                className="bg-primary rounded-full h-2"
                                style={{ width: `${percentage}%` }}
                              />
                            </div>
                            <span className="text-sm">{percentage}%</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              {/* Reviews */}
              <section className="bg-white rounded-xl p-6 shadow-sm">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-2xl font-semibold">Reviews</h2>
                  <div className="flex items-center">
                    <Star className="text-yellow-400 mr-1" />
                    <span className="font-medium">{mockFranchise.rating}</span>
                  </div>
                </div>
                <div className="space-y-4">
                  {mockFranchise.reviews.map(review => (
                    <div key={review.id} className="border-t pt-4">
                      <div className="flex items-center mb-2">
                        <div className="relative h-10 w-10">
                          <Image
                            src={review.userAvatar}
                            alt={review.userName}
                            fill
                            className="object-cover rounded-full"
                          />
                        </div>
                        <div className="ml-3">
                          <h3 className="font-medium">{review.userName}</h3>
                          <p className="text-sm text-gray-500">
                            {new Date(review.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="ml-auto flex items-center">
                          <Star className="text-yellow-400 mr-1" />
                          <span>{review.rating}</span>
                        </div>
                      </div>
                      <p className="text-gray-700">{review.comment}</p>
                    </div>
                  ))}
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-20">
                {/* Franchise Brief */}
                <section className="bg-white rounded-xl p-6 shadow-sm">
                  <div className="flex items-start justify-between">
                    <div>
                      <h1 className="text-2xl font-bold">{mockFranchise.name}</h1>
                      <p className="text-gray-600 mt-2">
                        {mockFranchise.industry} • {mockFranchise.category}
                      </p>
                      <div className="flex items-center mt-2 text-gray-600">
                        <MapPin className="mr-2 h-4 w-4" />
                        {mockFranchise.address}
                      </div>
                    </div>
                    <div className="relative rounded overflow-hidden h-14 w-14">
                      <Image
                        src={mockFranchise.logo}
                        alt={`${mockFranchise.name} logo`}
                        fill
                        className="object-contain"
                      />
                    </div>
                  </div>
                  <p className="mt-4 text-gray-700 text-sm">{mockFranchise.description}</p>
                  
                  {/* Project Counts */}
                  <div className="grid grid-cols-3 gap-3 mt-6">
                    <div className="bg-green-50 rounded-lg p-3">
                      <p className="text-2xl font-semibold text-green-600">24</p>
                      <p className="text-xs text-green-700 mt-1">Outlets</p>
                    </div>
                    <div className="bg-yellow-50 rounded-lg p-3">
                      <p className="text-2xl font-semibold text-yellow-600">8</p>
                      <p className="text-xs text-yellow-700 mt-1">Launching</p>
                    </div>
                    <div className="bg-red-50 rounded-lg p-3">
                      <p className="text-2xl font-semibold text-red-600">12</p>
                      <p className="text-xs text-red-700 mt-1">Fundraising</p>
                    </div>
                  </div>

                  {/* Start New Franchise Button */}
                  <button className="w-full mt-6 bg-white border-2 border-primary text-primary hover:bg-primary hover:text-white py-2.5 rounded-lg font-medium transition-colors flex items-center justify-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="12" y1="5" x2="12" y2="19"></line>
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                    Start New Franchise
                  </button>
                </section>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}

export default FranchiseDetail;