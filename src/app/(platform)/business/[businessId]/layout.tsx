import BusinessSideNav from '@/components/BusinessSideNav';
import React from 'react'

export default async function BusinessLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{
    businessId: string;
  }>;
}) {
  const resolvedParams = await params;
  const businessId = resolvedParams.businessId;

  return (
    <div className="pt-[50px]">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <BusinessSideNav businessId={businessId} />
          {/* Main Content */}
          {children}
        </div>
      </div>
    </div>
  )
}