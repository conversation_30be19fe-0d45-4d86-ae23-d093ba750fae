'use client'

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { Store } from 'lucide-react';
import { createBrowserClient } from '@supabase/ssr'

interface Franchise {
  id: string;
  name: string;
  location: string;
  area: number;
  investment_amount: number;
  business_id: string;
  created_at: string;
}

interface BusinessPageClientProps {
  businessId: string;
}

export default function BusinessPageClient({ businessId }: BusinessPageClientProps) {
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
  const [visibleFranchisees, setVisibleFranchisees] = useState(4);
  const [franchises, setFranchises] = useState<Franchise[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchFranchises = async () => {
      try {
        const { data, error } = await supabase
          .from('franchises')
          .select('*')
          .eq('business_id', businessId)
          .order('created_at', { ascending: true });

        if (error) throw error;
        setFranchises(data || []);
      } catch (error) {
        console.error('Error fetching franchises:', error);
      } finally {
        setLoading(false);
      }
    };

    if (businessId) {
      fetchFranchises();
    }
  }, [businessId, supabase]);

  const loadMoreFranchisees = () => {
    setVisibleFranchisees(prev => prev + 4);
  };

  const getStatusByIndex = (index: number) => {
    const statuses = [
      { text: 'Active', classes: 'bg-green-100 dark:bg-green-900/50 text-green-800' },
      { text: 'In Progress', classes: 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-800' },
      { text: 'Planning', classes: 'bg-blue-100 dark:bg-blue-900/50 text-blue-800' }
    ];
    return statuses[index % 3];
  };

  const getProgressPercentage = (index: number) => {
    const percentages = [75, 45, 90, 30, 60];
    return percentages[index % percentages.length];
  };

  return (
    <div className="lg:col-span-3 space-y-8">
      {/* Franchise List */}
      <section className="bg-white dark:bg-stone-800 rounded-xl p-6 shadow-sm">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-semibold">Franchise</h2>
          <div className="flex items-center">
            <Store className="mr-1" />
          </div>
        </div>
        <div className="space-y-4">
          {loading ? (
            <div className="text-center py-4">Loading franchises...</div>
          ) : franchises.length === 0 ? (
            <div className="text-center py-4">No franchises found</div>
          ) : (
            franchises.slice(0, visibleFranchisees).map((franchise, index) => (
              <div key={franchise.id} className="border-t pt-4">
                <div className="flex items-center space-x-4">
                  <div className="relative h-24 w-40 rounded-lg overflow-hidden">
                    <Image
                      src={"/franchise/hubcv-1-1.png"}
                      alt={franchise.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="flex-grow">
                    <div className="flex items-center justify-between mb-2">
                      <h2 className="font-medium text-lg">{franchise.name}</h2>
                      <div className="flex items-center space-x-2">
                        <span className={`px-3 py-1 rounded-full text-sm ${getStatusByIndex(index).classes}`}>
                          {getStatusByIndex(index).text}
                        </span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-500">
                      {franchise.location}
                    </p>
                    {/* Monthly Earnings */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm dark:text-gray-400 text-gray-500">Area:</span>
                        <span className="text-sm font-semibold dark:text-gray-100 text-gray-900">
                          {franchise.area} sq ft
                        </span>
                        <span className="text-sm dark:text-gray-400 text-gray-500">•</span>
                        <span className="text-sm dark:text-gray-400 text-gray-500">Investment:</span>
                        <span className="text-sm font-semibold dark:text-gray-100 text-gray-900">
                          ₹{franchise.investment_amount.toLocaleString()}
                        </span>
                      </div>
                    </div>
                    {/* Progress Bar */}
                    <div className="mt-2">
                      <div className="w-full bg-gray-200 dark:bg-stone-700 rounded-full h-2">
                        <div 
                          className="bg-primary rounded-full h-2 transition-all duration-300"
                          style={{ width: `${getProgressPercentage(index)}%` }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
        {/* Load More Button */}
        {!loading && franchises.length > visibleFranchisees && (
          <div className="mt-6 text-center">
            <button 
              onClick={loadMoreFranchisees}
              className="inline-flex items-center px-4 py-2 border border-primary text-sm font-medium rounded-md text-primary hover:bg-primary hover:text-white transition-colors duration-200"
            >
              Load More
              <svg className="ml-2 -mr-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>
        )}
      </section>
    </div>
  );
} 