import { cookies } from 'next/headers'
import { createServerClient } from '@supabase/ssr'
import ProfilePageClient from './ProfilePageClient'

export default async function ProfilePage() {
  const cookieStore = await cookies()

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
      },
    }
  )

  const { data: franchises, error } = await supabase
    .from('franchises')
    .select('*')
    .order('created_at', { ascending: true });

  if (error) {
    console.error('Error fetching franchises:', error);
    return <div>Error loading franchises</div>;
  }

  return <ProfilePageClient initialFranchises={franchises || []} />;
}