import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export type Profile = {
  id: string
  avatar: string
  full_name: string
  location: string
  formatted_address: string
  area: string
  district: string
  state: string
  country: string
  pincode: string
  profession: string
  annual_income: string
  investment_budget: string
  created_at: string
  updated_at: string
} 