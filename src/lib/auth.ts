import { supabase } from './supabase'

export interface UserProfile {
  id: string;
  avatar: string;
  full_name: string;
  location: string;
  formatted_address: string;
  area: string;
  district: string;
  state: string;
  country: string;
  pincode: string;
  profession: string;
  annual_income: string;
  investment_budget: string;
  created_at?: string;
  updated_at?: string;
}

export interface CreateUserInput {
  phone: string;
  full_name: string;
  email?: string;
  avatar: string;
  location: string;
  formatted_address: string;
  area: string;
  district: string;
  state: string;
  country: string;
  pincode: string;
  profession: string;
  annual_income: string;
  investment_budget: string;
}

export const signInWithPhone = async (phone: string) => {
  const { data, error } = await supabase.auth.signInWithOtp({
    phone,
    options: {
      channel: 'sms'
    }
  })
  
  if (error) throw error
  return data
}

export const verifyOTP = async (phone: string, token: string) => {
  const { data, error } = await supabase.auth.verifyOtp({
    phone,
    token,
    type: 'sms'
  })
  
  if (error) throw error
  return data
}

export const signOut = async () => {
  const { error } = await supabase.auth.signOut()
  if (error) throw error
}

export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser()
  if (error) throw error
  return user
}

export const getUserProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single()

  if (error) throw error
  return data as UserProfile
}

export const createUserProfile = async (profile: Omit<UserProfile, 'id' | 'created_at' | 'updated_at'>) => {
  const user = await getCurrentUser()
  if (!user) throw new Error('No authenticated user')

  const { data, error } = await supabase
    .from('profiles')
    .upsert([
      {
        id: user.id,
        ...profile,
        updated_at: new Date().toISOString()
      }
    ])
    .select()
    .single()

  if (error) throw error
  return data as UserProfile
}

export const updateUserProfile = async (userId: string, profile: Partial<UserProfile>) => {
  const { data, error } = await supabase
    .from('profiles')
    .update(profile)
    .eq('id', userId)
    .select()
    .single()

  if (error) throw error
  return data as UserProfile
}

export const createUserWithProfile = async (userData: CreateUserInput) => {
  const user = await getCurrentUser()
  if (!user) throw new Error('No authenticated user')

  // Update auth user metadata
  const { data: updatedUser, error: updateError } = await supabase.auth.updateUser({
    email: userData.email,
    data: {
      full_name: userData.full_name,
      avatar_url: userData.avatar,
      phone: userData.phone
    }
  })

  if (updateError) throw updateError

  // Create or update profile
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .upsert([
      {
        id: user.id,
        avatar: userData.avatar,
        full_name: userData.full_name,
        location: userData.location,
        formatted_address: userData.formatted_address,
        area: userData.area,
        district: userData.district,
        state: userData.state,
        country: userData.country,
        pincode: userData.pincode,
        profession: userData.profession,
        annual_income: userData.annual_income,
        investment_budget: userData.investment_budget,
        updated_at: new Date().toISOString()
      }
    ])
    .select()
    .single()

  if (profileError) throw profileError

  return {
    user: updatedUser.user,
    profile: profile as UserProfile
  }
}

export async function getUserBusinesses(userId: string) {
  const { data: businesses, error } = await supabase
    .from('businesses')
    .select('*')
    .eq('owner_id', userId)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching businesses:', error)
    throw error
  }

  return businesses
} 