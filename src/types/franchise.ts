export type FranchiseStatus = 'Fundraising' | 'Launching' | 'Live' | 'Closed';

export interface Review {
  id: string;
  userId: string;
  userName: string;
  userAvatar: string;
  rating: number;
  comment: string;
  createdAt: string;
}

export interface FinancialProjection {
  year: number;
  revenue: number;
  expenses: number;
  profit: number;
  roi: number;
}

export interface LocationAnalysis {
  footfall: number;
  peakHours: string[];
  demographicData: {
    ageGroups: Record<string, number>;
    income: Record<string, number>;
  };
  competitorCount: number;
  marketPotential: number;
}

export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  category: string;
}

export interface Franchisee {
  id: string;
  name: string;
  avatar: string;
  joinedDate: string;
  shareCount: number;
}

export interface Franchise {
  id: string;
  name: string;
  username: string;
  coverPhoto: string;
  gallery: string[];
  logo: string;
  industry: string;
  category: string;
  address: string;
  description: string;
  totalInvestment: number;
  carpetArea: number;
  costPerShare: number;
  status: FranchiseStatus;
  totalShares: number;
  soldShares: number;
  minimumShares: number;
  products: Product[];
  franchisees: Franchisee[];
  financialProjections: FinancialProjection[];
  locationAnalysis: LocationAnalysis;
  reviews: Review[];
  rating: number;
} 