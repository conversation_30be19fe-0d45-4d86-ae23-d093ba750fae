declare module '@uploadcare/react-widget' {
  import { ComponentType } from 'react'

  interface WidgetProps {
    publicKey: string
    onChange: (file: { cdnUrl: string }) => void
    tabs?: string
    previewStep?: boolean
    clearable?: boolean
    imageShrink?: string
    crop?: string
    imagesOnly?: boolean
    validators?: Array<(file: File) => boolean>
  }

  interface LocaleSettings {
    locale?: string
  }

  export const Widget: ComponentType<WidgetProps>
} 