export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      businesses: {
        Row: {
          category_id: string
          created_at: string
          id: string
          industry_id: string
          logo_url: string | null
          min_area_needed: number
          min_investment_per_area: number
          min_team_size: number
          name: string
          owner_id: string
          serviceable_countries: string[]
          short_description: string | null
          slug: string
          total_active_franchise: number
          updated_at: string
        }
        Insert: {
          category_id: string
          created_at?: string
          id?: string
          industry_id: string
          logo_url?: string | null
          min_area_needed: number
          min_investment_per_area: number
          min_team_size?: number
          name: string
          owner_id: string
          serviceable_countries?: string[]
          short_description?: string | null
          slug: string
          total_active_franchise?: number
          updated_at?: string
        }
        Update: {
          category_id?: string
          created_at?: string
          id?: string
          industry_id?: string
          logo_url?: string | null
          min_area_needed?: number
          min_investment_per_area?: number
          min_team_size?: number
          name?: string
          owner_id?: string
          serviceable_countries?: string[]
          short_description?: string | null
          slug?: string
          total_active_franchise?: number
          updated_at?: string
        }
      }
      categories: {
        Row: {
          created_at: string
          id: string
          industry_id: string
          name: string
          slug: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          industry_id: string
          name: string
          slug: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          industry_id?: string
          name?: string
          slug?: string
          updated_at?: string
        }
      }
      industries: {
        Row: {
          created_at: string
          id: string
          name: string
          slug: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          name: string
          slug: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
          slug?: string
          updated_at?: string
        }
      }
    }
  }
} 