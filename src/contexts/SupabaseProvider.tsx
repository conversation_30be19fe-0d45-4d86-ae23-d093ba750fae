'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { User } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'

interface Profile {
  id: string
  avatar_url?: string
  full_name?: string
}

interface SupabaseContextType {
  user: User | null
  isAuthenticated: boolean
  profile: Profile | null
}

const SupabaseContext = createContext<SupabaseContextType>({
  user: null,
  isAuthenticated: false,
  profile: null
})

export function SupabaseProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)

  useEffect(() => {
    // Check current session
    supabase.auth.getSession().then(({ data: { session } }) => {
      const currentUser = session?.user ?? null
      setUser(currentUser)
      
      if (currentUser) {
        supabase
          .from('profiles')
          .select('id, avatar_url, full_name')
          .eq('id', currentUser.id)
          .single()
          .then(({ data }) => setProfile(data))
      }
    })

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (_event, session) => {
      const currentUser = session?.user ?? null
      setUser(currentUser)
      
      if (currentUser) {
        const { data } = await supabase
          .from('profiles')
          .select('id, avatar_url, full_name')
          .eq('id', currentUser.id)
          .single()
        setProfile(data)
      } else {
        setProfile(null)
      }
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  return (
    <SupabaseContext.Provider value={{
      user,
      isAuthenticated: !!user,
      profile
    }}>
      {children}
    </SupabaseContext.Provider>
  )
}

export const useSupabase = () => {
  return useContext(SupabaseContext)
} 