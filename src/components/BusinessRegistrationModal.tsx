'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { Widget } from '@uploadcare/react-widget'
import { useSupabase } from '@/contexts/SupabaseProvider'
import { supabase } from '@/lib/supabase'
import { X, Upload } from 'lucide-react'
import Select, { MultiValue } from 'react-select'
import countryList from 'react-select-country-list'
import { toast } from 'sonner'
import { Database } from '@/types/supabase'
import Image from 'next/image'
import { useRouter } from 'next/navigation'

interface BusinessRegistrationModalProps {
  isOpen: boolean
  onClose: () => void
}

type Industry = Database['public']['Tables']['industries']['Row']
type Category = Database['public']['Tables']['categories']['Row']
type BusinessInsert = Database['public']['Tables']['businesses']['Insert']

type Option = {
  label: string;
  value: string;
}

interface UploadcareFile {
  cdnUrl: string;
  name?: string;
  size?: number;
  isImage?: boolean;
  originalUrl?: string;
}

export default function BusinessRegistrationModal({ isOpen, onClose }: BusinessRegistrationModalProps) {
  const { user } = useSupabase()
  const router = useRouter()
  const [industries, setIndustries] = useState<Industry[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [selectedIndustry, setSelectedIndustry] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const countries = countryList().getData()

  const [formData, setFormData] = useState<Omit<BusinessInsert, 'owner_id'>>({
    name: '',
    slug: '',
    logo_url: '',
    industry_id: '',
    category_id: '',
    total_active_franchise: 0,
    serviceable_countries: [],
    min_area_needed: 0,
    min_investment_per_area: 0,
    min_team_size: 1,
    short_description: ''
  })

  const [logoPreview, setLogoPreview] = useState<string | null>(null)

  const fetchIndustries = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('industries')
        .select('*')
        .order('name')
      
      if (error) throw error
      setIndustries(data)
    } catch (error) {
      console.error('Error fetching industries:', error)
      toast.error('Failed to load industries')
    }
  }, [supabase])

  const fetchCategories = useCallback(async (industryId: string) => {
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .eq('industry_id', industryId)
        .order('name')
      
      if (error) throw error
      setCategories(data)
    } catch (error) {
      console.error('Error fetching categories:', error)
      toast.error('Failed to load categories')
    }
  }, [supabase])

  useEffect(() => {
    if (isOpen) {
      fetchIndustries()
    }
  }, [isOpen, fetchIndustries])

  useEffect(() => {
    if (selectedIndustry) {
      fetchCategories(selectedIndustry)
    }
  }, [selectedIndustry, fetchCategories])

  // Auto-generate slug when business name changes
  useEffect(() => {
    if (formData.name) {
      const generatedSlug = generateSlug(formData.name)
      setFormData(prev => ({ ...prev, slug: generatedSlug }))
    }
  }, [formData.name])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target
    if (name === 'industry_id') {
      setSelectedIndustry(value)
      setFormData(prev => ({
        ...prev,
        industry_id: value,
        category_id: '' // Reset category when industry changes
      }))
      if (value) {
        fetchCategories(value)
      }
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }))
    }
  }

  const handleCountryChange = (selectedOptions: MultiValue<Option>) => {
    setFormData(prev => ({
      ...prev,
      serviceable_countries: selectedOptions.map((option) => option.value)
    }))
  }

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)+/g, '')
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      if (!user?.id) throw new Error('User not authenticated')

      const { data, error } = await supabase
        .from('businesses')
        .insert([
          {
            ...formData,
            owner_id: user.id
          }
        ])
        .select('slug')
        .single()

      if (error) throw error

      toast.success('Business registered successfully')
      onClose()
      
      // Navigate to the business page using the slug
      if (data?.slug) {
        router.push(`/${data.slug}`)
      }
    } catch (error) {
      console.error('Error registering business:', error)
      toast.error('Failed to register business')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Transition show={isOpen} as={React.Fragment}>
      <Dialog
        as="div"
        className="fixed inset-0 z-50 overflow-y-auto"
        onClose={onClose}
      >
        <div className="flex min-h-screen items-center justify-center">
          <Transition.Child
            as={React.Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/30" />
          </Transition.Child>

          <Transition.Child
            as={React.Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0 scale-95"
            enterTo="opacity-100 scale-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100 scale-100"
            leaveTo="opacity-0 scale-95"
          >
            <div className="relative bg-white dark:bg-stone-800 rounded-2xl max-w-3xl w-full mx-4 p-6">
              <button
                onClick={onClose}
                className="absolute right-4 top-4 text-gray-400 dark:text-gray-100 dark:hover:text-gray-500 hover:text-gray-500"
              >
                <X className="h-5 w-5" />
              </button>

              <Dialog.Title className="text-xl font-semibold dark:text-white/80 text-gray-900 mb-6">
                Register New Business
              </Dialog.Title>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-4">
                  {/* Logo Upload and Preview Row */}
                  <div className="flex items-start space-x-4">
                    {logoPreview ? (
                      <div className="relative w-32 h-32 rounded-lg overflow-hidden border border-gray-200">
                        <Image
                          src={logoPreview}
                          alt="Logo preview"
                          fill
                          className="object-cover"
                        />
                      </div>
                    ) : (
                      <div className="w-32 h-32 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                        <Upload className="w-8 h-8 dark:text-white/80 text-gray-400" />
                      </div>
                    )}
                    <div className="flex-1">
                      <label className="block text-sm font-medium dark:text-white/80 text-gray-700 mb-1">
                        Business Logo
                      </label>
                      <Widget
                        publicKey={process.env.NEXT_PUBLIC_UPLOADCARE_PUBLIC_KEY || ''}
                        onChange={(file: UploadcareFile) => {
                          if (file) {
                            const cdnUrl = file.cdnUrl + '-/scale_crop/300x300/center/'
                            setFormData(prev => ({ ...prev, logo_url: cdnUrl }))
                            setLogoPreview(cdnUrl)
                          }
                        }}
                        tabs="file camera url"
                        previewStep
                        clearable
                        imageShrink="300x300"
                        crop="300x300"
                      />
                    </div>
                  </div>

                  {/* Business Name and Slug Row */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium dark:text-white/90 text-gray-700 mb-1">
                        Business Name
                      </label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className="w-full rounded-lg border dark:border-stone-700 border-gray-300 px-3 py-2"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium dark:text-white/90 text-gray-700 mb-1">
                        URL Slug
                      </label>
                      <input
                        type="text"
                        name="slug"
                        value={formData.slug}
                        onChange={handleInputChange}
                        className="w-full rounded-lg border dark:border-stone-700 border-gray-300 px-3 py-2"
                        required
                        pattern="[a-z0-9-]+"
                        title="Only lowercase letters, numbers, and hyphens are allowed"
                      />
                    </div>
                  </div>

                  {/* Industry and Category Row */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium dark:text-white/90 text-gray-700 mb-1">
                        Industry
                      </label>
                      <select
                        name="industry_id"
                        value={formData.industry_id}
                        onChange={handleSelectChange}
                        className="w-full rounded-lg border dark:border-stone-700 border-gray-300 px-3 py-2"
                        required
                      >
                        <option value="">Select Industry</option>
                        {industries.map(industry => (
                          <option key={industry.id} value={industry.id}>
                            {industry.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium dark:text-white/90 text-gray-700 mb-1">
                        Category
                      </label>
                      <select
                        name="category_id"
                        value={formData.category_id}
                        onChange={handleSelectChange}
                        className="w-full rounded-lg border dark:border-stone-700 border-gray-300 px-3 py-2"
                        required
                        disabled={!selectedIndustry}
                      >
                        <option value="">Select Category</option>
                        {categories.map(category => (
                          <option key={category.id} value={category.id}>
                            {category.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Metrics Row */}
                  <div className="grid grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium dark:text-white/90 text-gray-700 mb-1">
                        Active Franchises
                      </label>
                      <input
                        type="number"
                        name="total_active_franchise"
                        value={formData.total_active_franchise}
                        onChange={handleInputChange}
                        min="0"
                        className="w-full rounded-lg border dark:border-stone-700 border-gray-300 px-3 py-2"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium dark:text-white/90 text-gray-700 mb-1">
                        Min Area (sq ft)
                      </label>
                      <input
                        type="number"
                        name="min_area_needed"
                        value={formData.min_area_needed}
                        onChange={handleInputChange}
                        min="0"
                        step="0.01"
                        className="w-full rounded-lg border dark:border-stone-700 border-gray-300 px-3 py-2"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium dark:text-white/90 text-gray-700 mb-1">
                        Min Investment
                      </label>
                      <input
                        type="number"
                        name="min_investment_per_area"
                        value={formData.min_investment_per_area}
                        onChange={handleInputChange}
                        min="0"
                        step="0.01"
                        className="w-full rounded-lg border dark:border-stone-700 border-gray-300 px-3 py-2"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium dark:text-white/90 text-gray-700 mb-1">
                        Min Team Size
                      </label>
                      <input
                        type="number"
                        name="min_team_size"
                        value={formData.min_team_size}
                        onChange={handleInputChange}
                        min="1"
                        className="w-full rounded-lg border dark:border-stone-700 border-gray-300 px-3 py-2"
                        required
                      />
                    </div>
                  </div>

                  {/* Serviceable Countries */}
                  <div>
                    <label className="block text-sm font-medium dark:text-white/90 text-gray-700 mb-1">
                      Serviceable Countries
                    </label>
                    <Select
                      isMulti
                      name="serviceable_countries"
                      options={countries}
                      className="basic-multi-select"
                      classNamePrefix="select"
                      onChange={handleCountryChange}
                    />
                  </div>

                  {/* Short Description */}
                  <div>
                    <label className="block text-sm font-medium dark:text-white/90 text-gray-700 mb-1">
                      Short Description
                    </label>
                    <textarea
                      name="short_description"
                      value={formData.short_description || ''}
                      onChange={handleInputChange}
                      rows={4}
                      className="w-full rounded-lg border dark:border-stone-700 border-gray-300 px-3 py-2"
                      required
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-4 py-2 text-sm font-medium dark:text-white/90 text-gray-700 hover:text-gray-800"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="px-4 py-2 bg-primary dark:bg-white/90 dark:text-gray-900 text-white rounded-lg text-sm font-medium hover:bg-primary/90 disabled:opacity-50"
                  >
                    {isLoading ? 'Registering...' : 'Register Business'}
                  </button>
                </div>
              </form>
            </div>
          </Transition.Child>
        </div>
      </Dialog>
    </Transition>
  )
} 