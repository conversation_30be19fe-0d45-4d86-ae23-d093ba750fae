import React, { useState } from 'react';
import Image from 'next/image';
import { X } from 'lucide-react';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  franchiseData: {
    name: string;
    logo: string;
    address: string;
    totalShares: number;
    soldShares: number;
    costPerShare: number;
  };
}

const PaymentModal = ({ isOpen, onClose, franchiseData }: PaymentModalProps) => {
  const [selectedShares, setSelectedShares] = useState(1);
  const availableShares = franchiseData.totalShares - franchiseData.soldShares;
  
  // Calculate amounts
  const subTotal = selectedShares * franchiseData.costPerShare;
  const serviceFee = subTotal * 0.15; // 15% service fee
  const gst = subTotal * 0.05; // 5% GST
  const totalAmount = subTotal + serviceFee + gst;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />
      <div className="relative bg-white rounded-2xl w-full max-w-md mx-4 overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Buy Shares</h2>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              <X className="h-5 w-5" />
            </button>
          </div>
          <div className="flex items-center gap-3">
            <div className="relative h-12 w-12 rounded-lg overflow-hidden">
              <Image
                src={franchiseData.logo}
                alt={franchiseData.name}
                fill
                className="object-contain"
              />
            </div>
            <div>
              <h3 className="font-medium">{franchiseData.name}</h3>
              <p className="text-sm text-gray-600">{franchiseData.address}</p>
            </div>
          </div>
        </div>

        {/* Share Selection */}
        <div className="p-6 border-b">
          <div className="mb-4">
            <p className="text-sm text-gray-600">Available Shares</p>
            <p className="text-lg font-semibold">{availableShares} shares</p>
            <p className="text-sm text-gray-600 mt-1">Cost per Share: ₹{franchiseData.costPerShare.toLocaleString()}</p>
          </div>

          <div className="space-y-4">
            <label className="block text-sm font-medium text-gray-700">
              Select number of shares
            </label>
            <div className="flex items-center gap-4">
              <input
                type="range"
                min="1"
                max={availableShares}
                value={selectedShares}
                onChange={(e) => setSelectedShares(Number(e.target.value))}
                className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-primary"
              />
              <span className="font-medium">{selectedShares}</span>
            </div>
          </div>
        </div>

        {/* Payment Details */}
        <div className="p-6 space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">Selected Shares</span>
              <span>{selectedShares}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Amount</span>
              <span>₹{subTotal.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Service Fee (15%)</span>
              <span>₹{serviceFee.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">GST (5%)</span>
              <span>₹{gst.toLocaleString()}</span>
            </div>
            <div className="h-px bg-gray-200 my-2" />
            <div className="flex justify-between font-semibold">
              <span>Total Amount</span>
              <span>₹{totalAmount.toLocaleString()}</span>
            </div>
          </div>

          <button className="w-full bg-primary text-white py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors">
            Proceed to Payment
          </button>
        </div>
      </div>
    </div>
  );
};

export default PaymentModal; 