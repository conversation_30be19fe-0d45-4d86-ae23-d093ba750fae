'use client'

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { Bell, Bolt, CreditCard, HandCoins, HeartHandshake, Lock, ShieldCheck, Store, Wallet } from 'lucide-react';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { Database } from '@/types/supabase'

type Business = Database['public']['Tables']['businesses']['Row']

interface BusinessSideNavProps {
  businessId: string;
}

function BusinessSideNav({ businessId }: BusinessSideNavProps) {
  const pathname = usePathname()
  const [business, setBusiness] = useState<Business | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function getBusiness() {
      try {
        const { data: business, error } = await supabase
          .from('businesses')
          .select('*')
          .eq('id', businessId)
          .single()

        if (error) {
          throw error
        }

        if (business) {
          setBusiness(business)
        }
      } catch (error) {
        console.error('Error loading business:', error)
      } finally {
        setLoading(false)
      }
    }

    getBusiness()
  }, [businessId])

  const isActive = (path: string) => {
    return pathname === path
  }

  if (loading) {
    return <div>Loading...</div>
  }

  return (
    <div className="lg:col-span-1">
              <div className="sticky top-20">
                {/* Business Brief */}
                <section className="bg-white dark:bg-stone-800 rounded-xl p-6 shadow-sm mb-6">
                  <div className="relative rounded overflow-hidden h-14 w-14 mb-4">
                    <Image
                      src={business?.logo_url || "/placeholder-business.png"}
                      alt={`${business?.name || 'Business'}'s logo`}
                      fill
                      className="object-contain"
                    />
                  </div>
                  <div className="flex items-start justify-between">
                    <div>
                      <h1 className="text-2xl font-bold">{business?.name || 'Loading...'}</h1>
                      <div className="flex items-center mt-1 text-gray-600">
                        {business?.serviceable_countries?.join(', ') || 'No locations set'}
                      </div>
                      <p className="text-gray-600">
                        Min Investment: ₹{business?.min_investment_per_area || '0'} / sq ft
                      </p>
                      <p className="text-gray-600">
                        Min Area: {business?.min_area_needed || '0'} sq ft
                      </p>
                      <p className="text-gray-600">
                        Active Franchises: {business?.total_active_franchise || '0'}
                      </p>
                    </div>
                  </div>
                </section>

                {/* Dashboard Menu */}
                <section className="bg-white dark:bg-stone-800 rounded-xl p-6 shadow-sm mb-6">
                  <h3 className="text-lg font-semibold mb-4">Dashboard</h3>
                  <nav className="space-y-2">
                    <Link 
                      href={`/business/${business?.id}/franchise`}
                      className={`flex items-center ${isActive(`/business/${business?.id}/franchise`) ? 'text-primary bg-primary/5' : 'text-gray-600'} hover:text-primary hover:bg-stone-50 p-2 rounded-lg transition-colors`}
                    >
                      <Store className="h-5 w-5 mr-3" />
                      Franchise
                    </Link>
                    <Link 
                      href={`/business/${business?.id}/deals`}
                      className={`flex items-center ${isActive(`/business/${business?.id}/deals`) ? 'text-primary bg-primary/5' : 'text-gray-600'} hover:text-primary hover:bg-stone-50 p-2 rounded-lg transition-colors`}
                    >
                      <HeartHandshake className="h-5 w-5 mr-3" />
                      Deals
                    </Link>
                    <Link 
                      href={`/business/${business?.id}/transactions`}
                      className={`flex items-center ${isActive(`/business/${business?.id}/transactions`) ? 'text-primary bg-primary/5' : 'text-gray-600'} hover:text-primary hover:bg-stone-50 p-2 rounded-lg transition-colors`}
                    >
                      <CreditCard className="h-5 w-5 mr-3" />
                      Transactions
                    </Link>
                    <Link 
                      href={`/business/${business?.id}/earnings`}
                      className={`flex items-center ${isActive(`/business/${business?.id}/earnings`) ? 'text-primary bg-primary/5' : 'text-gray-600'} hover:text-primary hover:bg-stone-50 p-2 rounded-lg transition-colors`}
                    >
                      <Wallet className="h-5 w-5 mr-3" />
                      Earnings
                    </Link>
                    <Link 
                      href={`/business/${business?.id}/payouts`}
                      className={`flex items-center ${isActive(`/business/${business?.id}/payouts`) ? 'text-primary bg-primary/5' : 'text-gray-600'} hover:text-primary hover:bg-stone-50 p-2 rounded-lg transition-colors`}
                    >
                      <HandCoins className="h-5 w-5 mr-3" />
                      Payouts
                    </Link>
                  </nav>
                </section>

                {/* Settings Menu */}
                <section className="bg-white dark:bg-stone-800 rounded-xl p-6 shadow-sm">
                  <h3 className="text-lg font-semibold mb-4">Settings</h3>
                  <nav className="space-y-2">
                    <Link 
                      href={`/business/${business?.id}/edit`}
                      className={`flex items-center ${isActive(`/business/${business?.id}/edit`) ? 'text-primary bg-primary/5' : 'text-gray-600'} hover:text-primary hover:bg-stone-50 p-2 rounded-lg transition-colors`}
                    >
                      <Bolt className="h-5 w-5 mr-3" />
                      Edit Business
                    </Link>
                    <Link 
                      href={`/business/${business?.id}/security`}
                      className={`flex items-center ${isActive(`/business/${business?.id}/security`) ? 'text-primary bg-primary/5' : 'text-gray-600'} hover:text-primary hover:bg-stone-50 p-2 rounded-lg transition-colors`}
                    >
                      <ShieldCheck className="h-5 w-5 mr-3" />
                      Security
                    </Link>
                    <Link 
                      href={`/business/${business?.id}/notifications`}
                      className={`flex items-center ${isActive(`/business/${business?.id}/notifications`) ? 'text-primary bg-primary/5' : 'text-gray-600'} hover:text-primary hover:bg-stone-50 p-2 rounded-lg transition-colors`}
                    >
                      <Bell className="h-5 w-5 mr-3" />
                      Notifications
                    </Link>
                    <Link 
                      href={`/business/${business?.id}/privacy`}
                      className={`flex items-center ${isActive(`/business/${business?.id}/privacy`) ? 'text-primary bg-primary/5' : 'text-gray-600'} hover:text-primary hover:bg-stone-50 p-2 rounded-lg transition-colors`}
                    >
                      <Lock className="h-5 w-5 mr-3" />
                      Privacy
                    </Link>
                  </nav>
                </section>
              </div>
            </div>
  )
}

export default BusinessSideNav