'use client'

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { Bell, Bolt, CreditCard, HandCoins, HeartHandshake, Lock, ShieldCheck, Star, Store, Wallet } from 'lucide-react';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { supabase, type Profile } from '@/lib/supabase';

function ProfileSideNav() {
  const pathname = usePathname()
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function getProfile() {
      try {
        const { data: profile, error } = await supabase
          .from('profiles')
          .select('*')
          .single()

        if (error) {
          throw error
        }

        if (profile) {
          setProfile(profile)
        }
      } catch (error) {
        console.error('Error loading profile:', error)
      } finally {
        setLoading(false)
      }
    }

    getProfile()
  }, [])

  const isActive = (path: string) => {
    return pathname === path
  }

  if (loading) {
    return <div>Loading...</div>
  }

  return (
    <div className="lg:col-span-1">
              <div className="sticky top-20">
                {/* Profile Brief */}
                <section className="bg-white dark:bg-stone-800 rounded-xl p-6 shadow-sm mb-4">
                  <div className="relative rounded-full overflow-hidden h-14 w-14 mb-4">
                    <Image
                      src={profile?.avatar || "/avatar/avatar-m-5.png"}
                      alt={`${profile?.full_name || 'User'}'s avatar`}
                      fill
                      className="object-contain"
                    />
                  </div>
                  <div className="flex items-start justify-between">
                    <div>
                      <h1 className="text-2xl font-bold">{profile?.full_name || 'Loading...'}</h1>
                      <div className="flex items-center mt-1 dark:text-gray-400 text-gray-600">
                        {profile?.district || 'District not set'} • {profile?.country || 'Country not set'}
                      </div>
                      <p className="text-gray-600 dark:text-gray-400">
                        {profile?.profession || 'Profession not set'} • ₹{profile?.investment_budget || '0'} / Month
                      </p>
                      
                    </div>
                    
                  </div>

                  {/* Start New Franchise Button */}
                  {/* <button className="w-full mt-6 bg-white border-2 border-primary text-primary hover:bg-primary hover:text-white py-2.5 rounded-lg font-medium transition-colors flex items-center justify-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="12" y1="5" x2="12" y2="19"></line>
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                    Start New Franchise
                  </button> */}
                  
                </section>

                {/* Dashboard Menu */}
                <section className="bg-white dark:bg-stone-800 rounded-xl p-6 shadow-sm mb-4">
                  <h3 className="text-lg font-semibold mb-4">Dashboard</h3>
                  <nav className="space-y-2">
                    {/* <a href="/dashboard/overview" className="flex items-center text-gray-600 hover:text-primary hover:bg-stone-50 p-2 rounded-lg transition-colors">
                      <PieChart className="h-5 w-5 mr-3" />
                      Overview
                    </a> */}
                    <Link 
                      href="/profile/franchise" 
                      className={`flex items-center ${isActive('/profile/franchise') ? 'text-primary bg-primary/5' : 'text-gray-600'} hover:text-primary dark:hover:text-primary dark:text-gray-400 dark:hover:bg-stone-700 hover:bg-stone-50 p-2 rounded-lg transition-colors`}
                    >
                      <Store className="h-5 w-5 mr-3" />
                      Franchise
                    </Link>
                    <Link 
                      href="/profile/deals" 
                      className={`flex items-center ${isActive('/profile/deals') ? 'text-primary bg-primary/5' : 'text-gray-600'} hover:text-primary dark:hover:text-primary dark:text-gray-400 dark:hover:bg-stone-700 hover:bg-stone-50 p-2 rounded-lg transition-colors`}
                    >
                      <HeartHandshake className="h-5 w-5 mr-3" />
                      Deals
                    </Link>
                    <Link 
                      href="/profile/transactions" 
                      className={`flex items-center ${isActive('/profile/transactions') ? 'text-primary bg-primary/5' : 'text-gray-600'} hover:text-primary dark:hover:text-primary dark:text-gray-400 dark:hover:bg-stone-700 hover:bg-stone-50 p-2 rounded-lg transition-colors`}
                    >
                      <CreditCard className="h-5 w-5 mr-3" />
                      Transactions
                    </Link>
                    <Link 
                      href="/profile/earnings" 
                      className={`flex items-center ${isActive('/profile/earnings') ? 'text-primary bg-primary/5' : 'text-gray-600'} hover:text-primary dark:hover:text-primary dark:text-gray-400 dark:hover:bg-stone-700 hover:bg-stone-50 p-2 rounded-lg transition-colors`}
                    >
                      <Wallet className="h-5 w-5 mr-3" />
                      Earnings
                    </Link>
                    <Link 
                      href="/profile/payouts" 
                      className={`flex items-center ${isActive('/profile/payouts') ? 'text-primary bg-primary/5' : 'text-gray-600'} hover:text-primary dark:hover:text-primary dark:text-gray-400 dark:hover:bg-stone-700 hover:bg-stone-50 p-2 rounded-lg transition-colors`}
                    >
                      <HandCoins className="h-5 w-5 mr-3" />
                      Payouts
                    </Link>
                  </nav>
                </section>

                {/* Settings Menu */}
                <section className="bg-white dark:bg-stone-800 rounded-xl p-6 shadow-sm">
                  <h3 className="text-lg font-semibold mb-4">Settings</h3>
                  <nav className="space-y-2">
                    <Link 
                      href="/profile/edit-profile" 
                      className={`flex items-center ${isActive('/profile/edit-profile') ? 'text-primary bg-primary/5' : 'text-gray-600'} hover:text-primary dark:hover:text-primary dark:text-gray-400 dark:hover:bg-stone-700 hover:bg-stone-50 p-2 rounded-lg transition-colors`}
                    >
                      <Bolt className="h-5 w-5 mr-3" />
                      Edit Profile
                    </Link>
                    <Link 
                      href="/profile/security" 
                      className={`flex items-center ${isActive('/profile/security') ? 'text-primary bg-primary/5' : 'text-gray-600'} hover:text-primary dark:hover:text-primary dark:text-gray-400 dark:hover:bg-stone-700 hover:bg-stone-50 p-2 rounded-lg transition-colors`}
                    >
                      <ShieldCheck className="h-5 w-5 mr-3" />
                      Security
                    </Link>
                    <Link 
                      href="/profile/notifications" 
                      className={`flex items-center ${isActive('/profile/notifications') ? 'text-primary bg-primary/5' : 'text-gray-600'} hover:text-primary dark:hover:text-primary dark:text-gray-400 dark:hover:bg-stone-700 hover:bg-stone-50 p-2 rounded-lg transition-colors`}
                    >
                      <Bell className="h-5 w-5 mr-3" />
                      Notifications
                    </Link>
                    <Link 
                      href="/profile/privacy" 
                      className={`flex items-center ${isActive('/profile/privacy') ? 'text-primary bg-primary/5' : 'text-gray-600'} hover:text-primary dark:hover:text-primary dark:text-gray-400 dark:hover:bg-stone-700 hover:bg-stone-50 p-2 rounded-lg transition-colors`}
                    >
                      <Lock className="h-5 w-5 mr-3" />
                      Privacy
                    </Link>
                    <Link 
                      href="/profile/delete" 
                      className={`flex items-center ${isActive('/profile/delete') ? 'text-red-700 dark:text-red-600 bg-red-50' : 'text-red-600'} hover:text-red-700 dark:hover:text-red-600 hover:bg-red-50 p-2 rounded-lg transition-colors`}
                    >
                      <Star className="h-5 w-5 mr-3" />
                      Delete Account
                    </Link>
                  </nav>
                </section>
              </div>
            </div>
  )
}

export default ProfileSideNav