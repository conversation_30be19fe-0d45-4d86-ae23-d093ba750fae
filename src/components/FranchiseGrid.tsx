'use client'

import React, { useState } from 'react';
import InfiniteS<PERSON>roll from 'react-infinite-scroll-component';
import FranchiseCard from './FranchiseCard';
import { Franchise } from '@/types/franchise';
import { fetchFranchises } from '@/app/actions/franchiseActions';

interface FranchiseGridProps {
  initialFranchises: Franchise[];
}

const FranchiseGrid: React.FC<FranchiseGridProps> = ({ initialFranchises }) => {
  const [franchises, setFranchises] = useState<Franchise[]>(initialFranchises);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);

  const loadMore = async () => {
    const nextPage = currentPage + 1;
    const newFranchises = await fetchFranchises(nextPage);
    
    if (newFranchises.length === 0) {
      setHasMore(false);
      return;
    }
    
    setCurrentPage(nextPage);
    setFranchises(prev => [...prev, ...newFranchises]);
  };

  return (
    <InfiniteScroll
      dataLength={franchises.length}
      next={loadMore}
      hasMore={hasMore}
      loader={
        <div className="flex justify-center py-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      }
      endMessage={
        <div className="text-center text-gray-500 py-4">
          No more franchises to load
        </div>
      }
    >
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-10 py-6">
        {franchises.map((franchise) => (
          <FranchiseCard key={franchise.id} franchise={franchise} />
        ))}
      </div>
    </InfiniteScroll>
  );
};

export default FranchiseGrid; 