'use client'

import React, { useState, useEffect, useRef } from 'react'
import { Search, HeartHandshake, CreditCard, Bell,  Store, Power, UserCircle } from 'lucide-react'
import PhoneVerificationModal from './PhoneVerificationModal'
import { getUserProfile, getUserBusinesses, UserProfile } from '@/lib/auth'
import Link from 'next/link'
import { ThemeSwitcher } from '@/components/theme-switcher'
import Image from 'next/image'
import { useSupabase } from '@/contexts/SupabaseProvider'
import BusinessRegistrationModal from './BusinessRegistrationModal'
import { Database } from '@/types/supabase'
import { supabase } from '@/lib/supabase'

type Business = Database['public']['Tables']['businesses']['Row']

function Header() {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isProfileOpen, setIsProfileOpen] = useState(false)
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false)
  const [isBusinessRegistrationOpen, setIsBusinessRegistrationOpen] = useState(false)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [userBusinesses, setUserBusinesses] = useState<Business[]>([])
  
  const { user, isAuthenticated } = useSupabase()

  // Refs for dropdown containers
  const profileRef = useRef<HTMLDivElement>(null)
  const notificationsRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const fetchUserData = async () => {
      if (user?.id) {
        try {
          const [profile, businesses] = await Promise.all([
            getUserProfile(user.id),
            getUserBusinesses(user.id)
          ])
          setUserProfile(profile)
          setUserBusinesses(businesses)
        } catch (error) {
          console.error('Error fetching user data:', error)
        }
      }
    }

    if (isAuthenticated) {
      fetchUserData()
    }
  }, [user?.id, isAuthenticated])

  // Handle click outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (profileRef.current && !profileRef.current.contains(event.target as Node)) {
        setIsProfileOpen(false)
      }
      if (notificationsRef.current && !notificationsRef.current.contains(event.target as Node)) {
        setIsNotificationsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Close other dropdowns when one is opened
  const handleDropdownToggle = (dropdown: boolean, setter: (value: boolean) => void) => {
    if (!dropdown) {
      setIsProfileOpen(false)
      setIsNotificationsOpen(false)
    }
    setter(!dropdown)
  }

  const handleSignOut = async () => {
    try {
      setIsProfileOpen(false) // Close the profile dropdown first
      const { error } = await supabase.auth.signOut()
      if (error) throw error
      
      // Force reload the page to clear any cached state
      window.location.href = '/'
    } catch (error) {
      console.error('Error signing out:', error)
      // You might want to add a toast notification here to show the error
    }
  }

  return (
    <>
      <header className="fixed w-full bg-background-light dark:bg-stone-800 z-50 py-3 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <Link href="/">
              <div className="flex items-center cursor-pointer">
                <Image src="/logo.svg" alt="logo" width={32} height={32} />
                <span className="ml-4 font-bold text-xl text-primary dark:text-primary-light hidden sm:block">
                  FRANCHISEEN
                </span>
              </div>
            </Link>

            {/* Search Bar */}
            <div className="flex items-center justify-center flex-1">
              <div className="w-auto">
                <div className="relative flex items-center rounded-full border hover:shadow-md transition-all duration-300">
                  <input
                    type="text"
                    placeholder="Search"
                    className="w-[200px] py-1.5 pl-4 pr-10 rounded-full outline-none text-sm focus:w-[300px] transition-all duration-300 bg-white dark:bg-stone-700"
                  />
                  <div className="absolute right-1.5 bg-primary p-1.5 rounded-full dark:bg-stone-800 text-white cursor-pointer hover:bg-primary-dark transition-colors">
                    <Search className="h-3 w-3" />
                  </div>
                </div>
              </div>
            </div>

            {/* Right Navigation */}
            <div className="flex items-center space-x-2">
              <ThemeSwitcher />

              {isAuthenticated ? (
                <>
                  {/* Notifications Dropdown */}
                  <div className="relative" ref={notificationsRef}>
                    <button 
                      onClick={() => handleDropdownToggle(isNotificationsOpen, setIsNotificationsOpen)}
                      className="hover:bg-gray-100 dark:hover:bg-stone-700 p-2 rounded-full transition-colors duration-200"
                    >
                      <Bell className="h-5 w-5" />
                      <span className="absolute top-0 right-0 h-2 w-2 bg-primary rounded-full"></span>
                    </button>
                    <div className={`absolute right-0 mt-3 w-80 dark:bg-stone-800 bg-white rounded-2xl shadow-[0_8px_30px_rgb(0,0,0,0.12)] border-0 py-4 transform transition-all duration-200 origin-top-right ${
                      isNotificationsOpen ? 'scale-100 opacity-100' : 'scale-95 opacity-0 pointer-events-none'
                    }`}>
                      <div className="px-6 pb-4 border-b">
                        <div className="flex justify-between items-center">
                          <h3 className="text-lg font-semibold dark:text-gray-100 text-gray-900">Notifications</h3>
                          <span className="bg-primary/10 text-primary text-xs px-2.5 py-1 rounded-full font-medium">2 new</span>
                        </div>
                      </div>
                      <div className="max-h-[calc(100vh-200px)] overflow-y-auto">
                        <div className="px-6 py-4 border-b last:border-0 dark:hover:bg-stone-700 hover:bg-gray-50 transition-colors cursor-pointer">
                          <div className="flex gap-4">
                            <div className="mt-1 rounded-full bg-primary/10 p-2">
                              <HeartHandshake className="h-4 w-4 text-primary" />
                            </div>
                            <div>
                              <p className="font-medium text-gray-900 dark:text-gray-100">New Share Offer</p>
                              <p className="text-gray-600 dark:text-gray-400 text-sm mt-0.5">You received a new share purchase offer</p>
                              <p className="text-gray-400 dark:text-gray-600 text-xs mt-2">2 minutes ago</p>
                            </div>
                          </div>
                        </div>
                        <div className="px-6 py-4 border-b last:border-0 dark:hover:bg-stone-700 hover:bg-gray-50 transition-colors cursor-pointer">
                          <div className="flex gap-4">
                            <div className="mt-1 rounded-full dark:bg-green-900/50 bg-green-50 p-2">
                              <CreditCard className="h-4 w-4 text-green-600" />
                            </div>
                            <div>
                              <p className="font-medium text-gray-900 dark:text-gray-100">Monthly Earnings Update</p>
                              <p className="text-gray-600 dark:text-gray-400 text-sm mt-0.5">Your monthly earnings have been calculated</p>
                              <p className="text-gray-400 dark:text-gray-600 text-xs mt-2">1 hour ago</p>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="px-6 pt-4 border-t mt-2">
                        <button className="text-primary hover:text-primary/90 text-sm font-medium">
                          View all notifications
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Profile Dropdown */}
                  <div className="relative" ref={profileRef}>
                    <button 
                      onClick={() => handleDropdownToggle(isProfileOpen, setIsProfileOpen)}
                      className="hover:bg-gray-100 dark:hover:bg-stone-700 p-2 rounded-full transition-colors duration-200"
                    >
                      <UserCircle className="h-5 w-5" />
                      {/* <span className="absolute top-0 right-0 h-2 w-2 bg-primary rounded-full"></span> */}
                    </button>
                    <div className={`absolute right-0 mt-3 w-72 dark:bg-stone-800 bg-white rounded-2xl shadow-[0_8px_30px_rgb(0,0,0,0.12)] border-0 overflow-hidden transform transition-all duration-200 origin-top-right ${
                      isProfileOpen ? 'scale-100 opacity-100' : 'scale-95 opacity-0 pointer-events-none'
                    }`}>
                      
                      <div className="border-t">
                        <Link 
                          href="/profile/franchise" 
                          className="flex items-center gap-3 px-5 py-3 text-gray-700 dark:text-gray-100 dark:hover:bg-stone-700 hover:bg-gray-50 transition-colors"
                        >
                          <div className="relative h-10 w-10 flex-shrink-0">
                            <Image
                              src={userProfile?.avatar || user?.user_metadata?.avatar || '/avatar/avatar-m-2.png'}
                              alt={`avatar`}
                              fill
                              className="object-cover rounded-full ring-2 ring-gray-100 dark:ring-stone-700"
                            />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="text-base font-semibold dark:text-gray-100 text-gray-900 truncate">
                              {userProfile?.full_name || user?.user_metadata?.full_name}
                            </h3>
                            <p className="text-sm text-gray-500 truncate mt-0.5">{user?.phone}</p>
                          </div>
                        </Link>
                      </div>

                      {userBusinesses.length > 0 && (
                        <div className="border-t">
                          <div className="px-5 py-2">
                            <h4 className="text-xs font-medium text-gray-500 uppercase">Your Businesses</h4>
                          </div>
                          {userBusinesses.map((business) => (
                            <Link 
                              key={business.id}
                              href={`/business/${business.id}`}
                              className="flex items-center gap-3 px-5 py-2 text-gray-700 dark:text-gray-100 dark:hover:bg-stone-700 hover:bg-gray-50 transition-colors"
                            >
                              <div className="relative h-8 w-8 flex-shrink-0">
                                <Image
                                  src={business.logo_url || '/placeholder-business.png'}
                                  alt={business.name}
                                  fill
                                  className="object-cover rounded ring-1 ring-gray-100 dark:ring-stone-700"
                                />
                              </div>
                              <div className="flex-1 min-w-0">
                                <h3 className="text-sm font-medium truncate">{business.name}</h3>
                                <p className="text-xs text-gray-500 truncate mt-0.5">
                                  Active
                                </p>
                              </div>
                            </Link>
                          ))}
                        </div>
                      )}

                      <div className="border-t">
                        <Link 
                          href="/business/register" 
                          className="flex items-center gap-4 px-6 py-3 text-gray-700 dark:text-gray-100 dark:hover:bg-stone-700 hover:bg-gray-50 transition-colors"
                          onClick={(e) => {
                            e.preventDefault()
                            setIsBusinessRegistrationOpen(true)
                          }}
                        >
                          <Store className="h-5 w-5 dark:text-gray-400 text-gray-400" />
                          <span className="text-sm font-medium">Register New Franchise</span>
                        </Link>
                      </div>
                      <div className="border-t">
                        <button 
                          className="w-full flex items-center gap-4 px-6 py-3 text-gray-700 dark:text-gray-100 dark:hover:bg-red-900/50 hover:bg-red-50 transition-colors"
                          onClick={handleSignOut}
                        >
                          <Power className="h-5 w-5 dark:text-red-400 text-red-400" />
                          <span className="text-sm text-red-600 dark:text-red-400 font-medium">Sign Out</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <button className="hidden cursor-pointer sm:block hover:bg-gray-100 dark:hover:bg-stone-700 px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200">
                    How it works
                  </button>
                  <button 
                    onClick={() => setIsModalOpen(true)}
                    className="hidden cursor-pointer sm:block bg-white text-primary dark:text-stone-800 hover:bg-stone-100 dark:hover:bg-stone-700 dark:hover:text-white px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200"
                  >
                    Get Started
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </header>

      <PhoneVerificationModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />

      <BusinessRegistrationModal
        isOpen={isBusinessRegistrationOpen}
        onClose={() => setIsBusinessRegistrationOpen(false)}
      />
    </>
  )
}

export default Header