{"name": "client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^1.7.18", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@supabase/ssr": "^0.1.0", "@supabase/supabase-js": "^2.39.8", "@uploadcare/react-widget": "^2.4.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "geist": "^1.2.0", "lucide-react": "^0.358.0", "next": "14.1.3", "next-themes": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-infinite-scroll-component": "^6.1.0", "react-select": "^5.10.1", "react-select-country-list": "^2.2.3", "sonner": "^1.4.3", "tailwind-merge": "^2.2.1", "tailwindcss": "^3.4.17"}, "devDependencies": {"@types/node": "^20", "@types/react": "18.2.64", "@types/react-dom": "18.2.21", "@types/react-select-country-list": "^2.2.3", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.3", "postcss": "^8", "typescript": "^5"}}